// Test script to verify password validation is working
const testCases = [
  {
    name: "Weak password - too short",
    password: "123",
    shouldFail: true
  },
  {
    name: "Weak password - no uppercase",
    password: "password123!",
    shouldFail: true
  },
  {
    name: "Weak password - no lowercase", 
    password: "PASSWORD123!",
    shouldFail: true
  },
  {
    name: "Weak password - no numbers",
    password: "Password!",
    shouldFail: true
  },
  {
    name: "Weak password - no special chars",
    password: "Password123",
    shouldFail: true
  },
  {
    name: "Strong password - meets all criteria",
    password: "MySecure123!",
    shouldFail: false
  }
];

async function testPasswordValidation() {
  console.log("Testing Password Validation...\n");
  
  for (const testCase of testCases) {
    console.log(`Testing: ${testCase.name}`);
    console.log(`Password: "${testCase.password}"`);
    
    try {
      const response = await fetch('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: `test${Date.now()}@example.com`,
          password: testCase.password,
          firstName: "Test",
          lastName: "User",
          account_type: "personal"
        }),
      });

      const data = await response.json();
      
      if (testCase.shouldFail) {
        if (!response.ok && data.error === 'Password does not meet requirements') {
          console.log("✅ PASS - Password correctly rejected");
          console.log(`   Reason: ${data.message}`);
        } else {
          console.log("❌ FAIL - Weak password was accepted!");
        }
      } else {
        if (response.ok) {
          console.log("✅ PASS - Strong password accepted");
        } else {
          console.log("❌ FAIL - Strong password was rejected!");
          console.log(`   Error: ${data.error || data.message}`);
        }
      }
    } catch (error) {
      console.log("❌ ERROR - Request failed:", error.message);
    }
    
    console.log(""); // Empty line for readability
  }
}

// Run the tests
testPasswordValidation().catch(console.error);
